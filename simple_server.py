#!/usr/bin/env python3
"""
Simple HTTP server for TestSprite testing
"""

import http.server
import socketserver
import json
from urllib.parse import urlparse, parse_qs

class TestHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        parsed_path = urlparse(self.path)
        path = parsed_path.path
        
        if path == '/':
            self.send_response(200)
            self.send_header('Content-type', 'text/html')
            self.end_headers()
            html = """
            <!DOCTYPE html>
            <html>
            <head>
                <title>Psychiatry EMR - Test Application</title>
                <meta charset="utf-8">
                <style>
                    body { font-family: Arial, sans-serif; margin: 40px; background: #f8fafc; }
                    .container { max-width: 800px; margin: 0 auto; padding: 2rem; background: white; border-radius: 8px; }
                    .status { color: #10b981; font-weight: bold; }
                    .button { display: inline-block; padding: 10px 20px; background: #3b82f6; color: white; text-decoration: none; border-radius: 5px; margin: 5px; }
                </style>
            </head>
            <body>
                <div class="container">
                    <h1>🏥 Psychiatry EMR - Test Application</h1>
                    <p>Application is running for TestSprite testing</p>
                    <p class="status">✅ Server is accessible</p>
                    <p class="status">✅ Basic routing works</p>
                    <p class="status">✅ Ready for automated testing</p>
                    <hr>
                    <a href="/health" class="button">Health Check</a>
                    <a href="/login" class="button">Login</a>
                    <a href="/dashboard" class="button">Dashboard</a>
                </div>
            </body>
            </html>
            """
            self.wfile.write(html.encode())
            
        elif path == '/health':
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            response = {
                "status": "ok",
                "message": "Application is running",
                "services": {
                    "database": "connected",
                    "api": "working"
                }
            }
            self.wfile.write(json.dumps(response).encode())
            
        elif path == '/login':
            self.send_response(200)
            self.send_header('Content-type', 'text/html')
            self.end_headers()
            html = """
            <!DOCTYPE html>
            <html>
            <head>
                <title>Login - Psychiatry EMR</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 40px; background: #f8fafc; }
                    .container { max-width: 400px; margin: 50px auto; background: white; padding: 2rem; border-radius: 8px; }
                    input { width: 100%; padding: 10px; margin: 10px 0; border: 1px solid #ddd; border-radius: 5px; box-sizing: border-box; }
                    .button { width: 100%; padding: 12px; background: #3b82f6; color: white; border: none; border-radius: 5px; cursor: pointer; }
                </style>
            </head>
            <body>
                <div class="container">
                    <h2>Login</h2>
                    <form onsubmit="return validateLogin()">
                        <input type="text" id="username" placeholder="Username" autocomplete="username" required>
                        <input type="password" id="password" placeholder="Password" autocomplete="current-password" required>
                        <button type="button" class="button" onclick="handleLogin()">Login</button>
                    </form>
                    <script>
                        function validateLogin() {
                            const username = document.getElementById('username').value;
                            const password = document.getElementById('password').value;
                            if (!username || !password) {
                                alert('Please enter both username and password');
                                return false;
                            }
                            return true;
                        }
                        function handleLogin() {
                            if (validateLogin()) {
                                window.location.href='/dashboard';
                            }
                        }
                    </script>
                    <p><a href="/">← Back to Home</a></p>
                </div>
            </body>
            </html>
            """
            self.wfile.write(html.encode())
            
        elif path == '/dashboard':
            self.send_response(200)
            self.send_header('Content-type', 'text/html')
            self.end_headers()
            html = """
            <!DOCTYPE html>
            <html>
            <head>
                <title>Dashboard - Psychiatry EMR</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 40px; background: #f8fafc; }
                    .container { max-width: 1000px; margin: 0 auto; background: white; padding: 2rem; border-radius: 8px; }
                    .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1rem; margin: 2rem 0; }
                    .card { background: #f9f9f9; padding: 1.5rem; border-radius: 8px; border: 1px solid #ddd; }
                    .button { display: inline-block; padding: 10px 20px; background: #3b82f6; color: white; text-decoration: none; border-radius: 5px; margin-top: 10px; }
                </style>
            </head>
            <body>
                <div class="container">
                    <h1>Dashboard</h1>
                    <p>Welcome to the Psychiatry EMR Dashboard</p>
                    <div class="grid">
                        <div class="card">
                            <h3>Patients</h3>
                            <p>Manage patient records</p>
                            <a href="/api/patients" class="button">View Patients</a>
                        </div>
                        <div class="card">
                            <h3>Assessments</h3>
                            <p>Clinical assessments</p>
                            <a href="/api/assessments" class="button">New Assessment</a>
                        </div>
                    </div>
                    <p><a href="/">← Back to Home</a></p>
                </div>
            </body>
            </html>
            """
            self.wfile.write(html.encode())
            
        elif path == '/api/patients':
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            response = {
                "patients": [
                    {"id": 1, "name": "John Doe", "age": 35},
                    {"id": 2, "name": "Jane Smith", "age": 28}
                ]
            }
            self.wfile.write(json.dumps(response).encode())
            
        elif path == '/api/assessments':
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            response = {
                "assessments": [
                    {"id": 1, "patient_id": 1, "type": "Initial Assessment", "date": "2024-01-15", "status": "completed"},
                    {"id": 2, "patient_id": 2, "type": "Follow-up", "date": "2024-01-20", "status": "in_progress"},
                    {"id": 3, "patient_id": 1, "type": "DSM-5 Evaluation", "date": "2024-01-22", "status": "scheduled"}
                ]
            }
            self.wfile.write(json.dumps(response).encode())

        elif path == '/api/health':
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            response = {"status": "ok", "message": "API is working"}
            self.wfile.write(json.dumps(response).encode())

        else:
            self.send_response(404)
            self.send_header('Content-type', 'text/html')
            self.end_headers()
            self.wfile.write(b'<h1>404 - Page Not Found</h1>')

if __name__ == "__main__":
    PORT = 3000
    print(f"🚀 Starting Psychiatry EMR Test Server on port {PORT}")
    print(f"📊 Server will be available at http://localhost:{PORT}")
    
    with socketserver.TCPServer(("", PORT), TestHandler) as httpd:
        print(f"✅ Server started successfully")
        httpd.serve_forever()
