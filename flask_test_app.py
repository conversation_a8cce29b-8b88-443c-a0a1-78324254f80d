#!/usr/bin/env python3
"""
Simple Flask application for TestSprite testing
Mimics the Psychiatry EMR interface for automated testing
"""

from flask import Flask, render_template_string, jsonify, request, redirect, url_for
import os

app = Flask(__name__)
app.secret_key = 'test_secret_key_for_development'

# HTML Templates
INDEX_TEMPLATE = """
<!DOCTYPE html>
<html>
<head>
    <title>Psychiatry EMR - Test Application</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f8fafc; }
        .container { max-width: 800px; margin: 0 auto; padding: 2rem; }
        .header { text-align: center; margin-bottom: 2rem; }
        .card { background: white; padding: 1.5rem; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); margin-bottom: 1rem; }
        .button { display: inline-block; padding: 10px 20px; background: #3b82f6; color: white; text-decoration: none; border-radius: 5px; margin: 5px; }
        .button:hover { background: #2563eb; }
        .green { background: #10b981; }
        .green:hover { background: #059669; }
        .status { color: #10b981; font-weight: bold; }
        .divider { border-top: 1px solid #e5e7eb; margin: 1rem 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏥 Psychiatry EMR - Test Application</h1>
            <p>Application is running for TestSprite testing</p>
        </div>
        
        <div class="card">
            <p class="status">✅ Server is accessible</p>
            <p class="status">✅ Basic routing works</p>
            <p class="status">✅ Ready for automated testing</p>
        </div>
        
        <div class="divider"></div>
        
        <div style="text-align: center;">
            <a href="/health" class="button">Health Check</a>
            <a href="/login" class="button">Login</a>
            <a href="/dashboard" class="button green">Dashboard</a>
        </div>
    </div>
</body>
</html>
"""

LOGIN_TEMPLATE = """
<!DOCTYPE html>
<html>
<head>
    <title>Login - Psychiatry EMR</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f8fafc; }
        .container { max-width: 400px; margin: 50px auto; }
        .card { background: white; padding: 2rem; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .form-group { margin-bottom: 1rem; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input { width: 100%; padding: 10px; border: 1px solid #d1d5db; border-radius: 5px; box-sizing: border-box; }
        .button { width: 100%; padding: 12px; background: #3b82f6; color: white; border: none; border-radius: 5px; cursor: pointer; font-size: 16px; }
        .button:hover { background: #2563eb; }
        .back-link { display: block; text-align: center; margin-top: 1rem; color: #3b82f6; text-decoration: none; }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <h2 style="text-align: center; margin-bottom: 2rem;">Login</h2>
            <form method="POST" action="/login">
                <div class="form-group">
                    <label for="username">Username:</label>
                    <input type="text" id="username" name="username" required>
                </div>
                <div class="form-group">
                    <label for="password">Password:</label>
                    <input type="password" id="password" name="password" required>
                </div>
                <button type="submit" class="button">Login</button>
            </form>
            <a href="/" class="back-link">← Back to Home</a>
        </div>
    </div>
</body>
</html>
"""

DASHBOARD_TEMPLATE = """
<!DOCTYPE html>
<html>
<head>
    <title>Dashboard - Psychiatry EMR</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f8fafc; }
        .container { max-width: 1000px; margin: 0 auto; padding: 2rem; }
        .header { text-align: center; margin-bottom: 2rem; }
        .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1rem; margin-bottom: 2rem; }
        .card { background: white; padding: 1.5rem; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .button { display: inline-block; padding: 10px 20px; background: #3b82f6; color: white; text-decoration: none; border-radius: 5px; margin-top: 10px; }
        .button.green { background: #10b981; }
        .button.purple { background: #8b5cf6; }
        .back-link { display: block; text-align: center; color: #3b82f6; text-decoration: none; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Dashboard</h1>
            <p>Welcome to the Psychiatry EMR Dashboard</p>
        </div>
        
        <div class="grid">
            <div class="card">
                <h3>Patients</h3>
                <p>Manage patient records</p>
                <a href="/patients" class="button">View Patients</a>
            </div>
            <div class="card">
                <h3>Assessments</h3>
                <p>Clinical assessments</p>
                <a href="/assessments" class="button green">New Assessment</a>
            </div>
            <div class="card">
                <h3>Reports</h3>
                <p>Generate reports</p>
                <a href="/reports" class="button purple">View Reports</a>
            </div>
        </div>
        
        <a href="/" class="back-link">← Back to Home</a>
    </div>
</body>
</html>
"""

@app.route('/')
def index():
    return render_template_string(INDEX_TEMPLATE)

@app.route('/health')
def health():
    return jsonify({
        "status": "ok",
        "message": "Application is running",
        "services": {
            "database": "connected",
            "api": "working",
            "authentication": "active"
        }
    })

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        # Simple test authentication
        if username and password:
            return redirect(url_for('dashboard'))
        else:
            return render_template_string(LOGIN_TEMPLATE)
    return render_template_string(LOGIN_TEMPLATE)

@app.route('/dashboard')
def dashboard():
    return render_template_string(DASHBOARD_TEMPLATE)

@app.route('/patients')
def patients():
    return jsonify({
        "patients": [
            {"id": 1, "name": "John Doe", "age": 35, "last_visit": "2024-01-15"},
            {"id": 2, "name": "Jane Smith", "age": 28, "last_visit": "2024-01-20"},
            {"id": 3, "name": "Bob Johnson", "age": 42, "last_visit": "2024-01-18"}
        ]
    })

@app.route('/assessments')
def assessments():
    return jsonify({
        "assessments": [
            {"id": 1, "patient_id": 1, "type": "Initial Assessment", "date": "2024-01-15"},
            {"id": 2, "patient_id": 2, "type": "Follow-up", "date": "2024-01-20"}
        ]
    })

@app.route('/reports')
def reports():
    return jsonify({
        "reports": [
            {"id": 1, "name": "Monthly Summary", "generated": "2024-01-31"},
            {"id": 2, "name": "Patient Statistics", "generated": "2024-01-30"}
        ]
    })

@app.route('/api/health')
def api_health():
    return jsonify({"status": "ok", "message": "API is working"})

@app.route('/api/patients')
def api_patients():
    return jsonify({
        "patients": [
            {"id": 1, "name": "John Doe", "age": 35},
            {"id": 2, "name": "Jane Smith", "age": 28}
        ]
    })

if __name__ == '__main__':
    print("🚀 Starting Psychiatry EMR Test Application...")
    print("📊 Server will be available at http://localhost:3000")
    app.run(host='127.0.0.1', port=3000, debug=True)
